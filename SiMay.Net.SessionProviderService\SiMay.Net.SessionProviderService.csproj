﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{61FB6C20-4541-40D1-AC4B-3ECDACF5F633}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>SiMay.Net.SessionProviderService</RootNamespace>
    <AssemblyName>SiMay.Net.SessionProviderService</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Bin\SessionService\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationConfiguration.cs" />
    <Compile Include="SystemOptionsDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemOptionsDialog.Designer.cs">
      <DependentUpon>SystemOptionsDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="ViewItem\ChannelViewItem.cs" />
    <Compile Include="SessionProviderService.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SessionProviderService.Designer.cs">
      <DependentUpon>SessionProviderService.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Win32.cs" />
    <EmbeddedResource Include="SystemOptionsDialog.resx">
      <DependentUpon>SystemOptionsDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SessionProviderService.resx">
      <DependentUpon>SessionProviderService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SiMay.Basic\SiMay.Basic.csproj">
      <Project>{8F2F35CB-D5EE-4D92-B42F-BCFFBF9C9D4F}</Project>
      <Name>SiMay.Basic</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Net.SessionProvider.Core\SiMay.Net.SessionProvider.Core.csproj">
      <Project>{0b2b697d-dd20-4869-836c-08c848e1813f}</Project>
      <Name>SiMay.Net.SessionProvider.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Net.SessionProviderServiceCore\SiMay.Net.SessionProviderServiceCore.csproj">
      <Project>{3c91f949-5187-4cdf-9a5b-aa3c20f7544b}</Project>
      <Name>SiMay.Net.SessionProviderServiceCore</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Icon\erro.ico" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Icon\ok.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>