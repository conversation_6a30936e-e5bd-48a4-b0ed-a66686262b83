﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8D061D46-9E59-42B0-91DC-4E380E4AE0BA}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiMay.Service.Core</RootNamespace>
    <AssemblyName>SiMay.Service.Core</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>发布\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Bin\Plugins\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\Bin\dat\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\Bin\dat\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup>
    <Reference Include="AForge.Video">
      <HintPath>..\AForge.dll\AForge.Video.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video.DirectShow">
      <HintPath>..\AForge.dll\AForge.Video.DirectShow.dll</HintPath>
    </Reference>
    <Reference Include="FFmpeg.AutoGen, Version=4.0.0.2, Culture=neutral, PublicKeyToken=9b7632533a381715, processorArchitecture=MSIL">
      <HintPath>..\packages\FFmpeg.AutoGen.4.0.0.2\lib\net45\FFmpeg.AutoGen.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Win32.Registry, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Registry.4.7.0\lib\net461\Microsoft.Win32.Registry.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Net" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationService\FileTransportService.cs" />
    <Compile Include="ApplicationService\RemoteDesktopService.cs" />
    <Compile Include="Attribute\ServiceName.cs" />
    <Compile Include="ApplicationService\RegistryEditorService.cs" />
    <Compile Include="ApplicationService\Registry\RegistryEditor.cs" />
    <Compile Include="ApplicationService\Registry\RegistrySeeker.cs" />
    <Compile Include="ApplicationService\StartupService.cs" />
    <Compile Include="ApplicationService\TcpConnectionService.cs" />
    <Compile Include="Base\MainApplicationServiceBase.cs" />
    <Compile Include="Base\RemoteSimpleServiceBase.cs" />
    <Compile Include="Custom\EventList.cs" />
    <Compile Include="GlobalMessageBus.cs" />
    <Compile Include="Helper\ImageExtensionHelper.cs" />
    <Compile Include="Helper\ServiceInstallerHelper.cs" />
    <Compile Include="Helper\SimpleServiceHelper.cs" />
    <Compile Include="Helper\SystemHelper.cs" />
    <Compile Include="ApplicationService\AForgeViedo.cs" />
    <Compile Include="AppConfiguration.cs" />
    <Compile Include="ApplicationService\AudioService.cs" />
    <Compile Include="ApplicationService\Keyboard.cs" />
    <Compile Include="ApplicationService\KeyboardService.cs" />
    <Compile Include="Extension\ServiceTypeExtension.cs" />
    <Compile Include="Custom\AwaitTaskSequence.cs" />
    <Compile Include="ApplicationService\ShellService.cs" />
    <Compile Include="ApplicationService\FileService.cs" />
    <Compile Include="Helper\AppDownloaderHelper.cs" />
    <Compile Include="Base\ApplicationProtocolService.cs" />
    <Compile Include="Base\ApplicationRemoteServiceBase.cs" />
    <Compile Include="Base\ApplicationServiceBase.cs" />
    <Compile Include="ApplicationService\ScreenService.cs" />
    <Compile Include="Helper\GetSystemInforHelper.cs" />
    <Compile Include="ApplicationService\SystemService.cs" />
    <Compile Include="ApplicationService\VideoService.cs" />
    <Compile Include="Interface\IRemoteSimpleService.cs" />
    <Compile Include="MainApplicationService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Interface\IApplicationService.cs" />
    <Compile Include="Helper\AppConfigRegValueHelper.cs" />
    <Compile Include="SimpleService\ActivateRemoteServiceSimpleService.cs" />
    <Compile Include="SimpleService\ConfiguartionSimpleService.cs" />
    <Compile Include="SimpleService\DesktopViewSimpleService.cs" />
    <Compile Include="SimpleService\ExecuteFileUpdateSimpleService.cs" />
    <Compile Include="SimpleService\MessageBoxSimpleService.cs" />
    <Compile Include="SimpleService\ShellSimpleService.cs" />
    <Compile Include="SimpleService\WebSimpleService.cs" />
    <Compile Include="SimpleService\WsStatusSimpleService.cs" />
    <Compile Include="SysUtil.cs" />
    <Compile Include="VideoEncoder\ErrorCodeHelper.cs" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SiMay.Basic\SiMay.Basic.csproj">
      <Project>{8f2f35cb-d5ee-4d92-b42f-bcffbf9c9d4f}</Project>
      <Name>SiMay.Basic</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Core.Standard\SiMay.Core.Standard.csproj">
      <Project>{48F71FA4-BDA3-4B2D-BC07-9F24CB9A6FB5}</Project>
      <Name>SiMay.Core.Standard</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.ModelBinder\SiMay.ModelBinder.csproj">
      <Project>{3547c677-4535-40bf-8185-f990c8b0d00f}</Project>
      <Name>SiMay.ModelBinder</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Net.SessionProvider\SiMay.Net.SessionProvider.csproj">
      <Project>{71283236-56CB-481E-A644-B7F9AF9EFDF9}</Project>
      <Name>SiMay.Net.SessionProvider</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Platform.Windows\SiMay.Platform.Windows.csproj">
      <Project>{B8B6A5BD-5349-4114-B93C-C1A3FD028F66}</Project>
      <Name>SiMay.Platform.Windows</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.RemoteService.Loader\SiMay.RemoteService.Loader.csproj">
      <Project>{7efe8058-f772-40eb-8e12-6e88e2a9e19a}</Project>
      <Name>SiMay.RemoteService.Loader</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Serialize.Standard\SiMay.Serialize.Standard.csproj">
      <Project>{9525A4AA-6731-4AB2-8CD0-ADDF7940FE32}</Project>
      <Name>SiMay.Serialize.Standard</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Sockets.Standard\SiMay.Sockets.Standard.csproj">
      <Project>{866F8FE0-EE58-4D38-8BE7-CBDD19DD1B40}</Project>
      <Name>SiMay.Sockets.Standard</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>