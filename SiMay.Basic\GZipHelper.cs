﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;

namespace SiMay.Basic
{
    public class GZipHelper
    {

        public static byte[] Compress(byte[] data, int offset, int lenght)
        {
            byte[] buffer = null;
            try
            {
                MemoryStream ms = new MemoryStream();
                GZipStream zip = new GZipStream(ms, CompressionMode.Compress, true);
                zip.Write(data, offset, lenght);
                zip.Close();
                buffer = new byte[ms.Length];
                ms.Position = 0;
                ms.Read(buffer, 0, buffer.Length);
                ms.Close();
            }
            catch { }
            return buffer;
        }
        public static byte[] Decompress(byte[] data)
            => Decompress(data, 0, data.Length);
        public static byte[] Decompress(byte[] data, int offset, int length)
        {
            byte[] buffer = null;
            try
            {
                MemoryStream ms = new MemoryStream(data, offset, length);
                GZipStream zip = new GZipStream(ms, CompressionMode.Decompress, true);
                MemoryStream msreader = new MemoryStream();
                buffer = new byte[0x1000];
                while (true)
                {
                    int reader = zip.Read(buffer, 0, buffer.Length);
                    if (reader <= 0)
                    {
                        break;
                    }
                    msreader.Write(buffer, 0, reader);
                }
                zip.Close();
                ms.Close();
                msreader.Position = 0;
                buffer = msreader.ToArray();
                msreader.Close();
            }
            catch { }
            return buffer;
        }
    }
}
