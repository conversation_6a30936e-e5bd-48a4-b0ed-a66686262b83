﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B8B6A5BD-5349-4114-B93C-C1A3FD028F66}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiMay.Platform.Windows</RootNamespace>
    <AssemblyName>SiMay.Platform.Windows</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ScreenSpy\DesktopCapture.cs" />
    <Compile Include="Entitys\RegValueData.cs" />
    <Compile Include="Enums\DifferStatus.cs" />
    <Compile Include="Extensions\RegistryKeyExtensions.cs" />
    <Compile Include="Helper\ByteConverterHelper.cs" />
    <Compile Include="Helper\DirectoryHelper.cs" />
    <Compile Include="Helper\FileIconUtil.cs" />
    <Compile Include="Helper\PlatformHelper.cs" />
    <Compile Include="Helper\RegistryKeyHelper.cs" />
    <Compile Include="Helper\RegValueHelper.cs" />
    <Compile Include="Helper\ScreenDpiHelper.cs" />
    <Compile Include="Helper\ServiceInstallerHelper.cs" />
    <Compile Include="Helper\SystemMessageNotifyHelper.cs" />
    <Compile Include="Helper\SystemSessionHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ScreenSpy\Capturer\BitBltCapture.cs" />
    <Compile Include="ScreenSpy\Capturer\ICapturer.cs" />
    <Compile Include="ScreenSpy\Entitys\Fragment.cs" />
    <Compile Include="ScreenSpy\JpgCompression.cs" />
    <Compile Include="ScreenSpy\ScreenSpy.cs" />
    <Compile Include="Win32\ADVAPI32.cs" />
    <Compile Include="Win32\CommonWin32Api.cs" />
    <Compile Include="Win32\GDI32.cs" />
    <Compile Include="Win32\Kernel32.cs" />
    <Compile Include="Win32\SECUR32.cs" />
    <Compile Include="Win32\User32.cs" />
    <Compile Include="Win32\Win32Interop.cs" />
    <Compile Include="Win32\WTSAPI32.cs" />
    <Compile Include="WinSound\Win32.cs" />
    <Compile Include="WinSound\WinSound.cs" />
    <Compile Include="WinSound\WinSoundPlayer.cs" />
    <Compile Include="WinSound\WinSoundRecord.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>