﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Win32.Registry" Version="4.7.0" />
    <PackageReference Include="System.Drawing.Common" Version="4.7.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SiMay.Basic\SiMay.Basic.csproj" />
    <ProjectReference Include="..\SiMay.ModelBinder\SiMay.ModelBinder.csproj" />
    <ProjectReference Include="..\SiMay.Net.SessionProvider\SiMay.Net.SessionProvider.csproj" />
    <ProjectReference Include="..\SiMay.Platform.Windows\SiMay.Platform.Windows.csproj" />
    <ProjectReference Include="..\SiMay.Serialize.Standard\SiMay.Serialize.Standard.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Management">
      <HintPath>..\..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Management.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
