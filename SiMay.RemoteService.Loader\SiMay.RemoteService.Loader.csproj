﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7EFE8058-F772-40EB-8E12-6E88E2A9E19A}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>SiMay.RemoteService.Loader</RootNamespace>
    <AssemblyName>SiMayService.Loader</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Bin\dat\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>8.0</LangVersion>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\Bin\dat\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>8.0</LangVersion>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Costura, Version=1.6.1.0, Culture=neutral, PublicKeyToken=9919ef960d84173d, processorArchitecture=MSIL">
      <HintPath>..\packages\Costura.Fody.1.6.1\lib\dotnet\Costura.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Entitys\LoaderAcknowledPacket.cs" />
    <Compile Include="Entitys\ServiceOptions.cs" />
    <Compile Include="Entitys\StartParameter.cs" />
    <Compile Include="Helper\MessageHelper.cs" />
    <Compile Include="Interface\IAppMainService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TrunkService\Packet\PacketEntity.cs" />
    <Compile Include="TrunkService\Service.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TrunkService\Service.designer.cs">
      <DependentUpon>Service.cs</DependentUpon>
    </Compile>
    <Compile Include="TrunkService\TrunkMessageHead.cs" />
    <Compile Include="TrunkService\UserTrunkContext.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="FodyWeavers.xml" />
    <None Include="Properties\app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SiMay.Basic\SiMay.Basic.csproj">
      <Project>{8F2F35CB-D5EE-4D92-B42F-BCFFBF9C9D4F}</Project>
      <Name>SiMay.Basic</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.ModelBinder\SiMay.ModelBinder.csproj">
      <Project>{3547c677-4535-40bf-8185-f990c8b0d00f}</Project>
      <Name>SiMay.ModelBinder</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Net.SessionProvider\SiMay.Net.SessionProvider.csproj">
      <Project>{71283236-56CB-481E-A644-B7F9AF9EFDF9}</Project>
      <Name>SiMay.Net.SessionProvider</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Platform.Windows\SiMay.Platform.Windows.csproj">
      <Project>{B8B6A5BD-5349-4114-B93C-C1A3FD028F66}</Project>
      <Name>SiMay.Platform.Windows</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Serialize.Standard\SiMay.Serialize.Standard.csproj">
      <Project>{9525a4aa-6731-4ab2-8cd0-addf7940fe32}</Project>
      <Name>SiMay.Serialize.Standard</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiMay.Sockets.Standard\SiMay.Sockets.Standard.csproj">
      <Project>{866F8FE0-EE58-4D38-8BE7-CBDD19DD1B40}</Project>
      <Name>SiMay.Sockets.Standard</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Fody.2.0.0\build\portable-net+sl+win+wpa+wp\Fody.targets" Condition="Exists('..\packages\Fody.2.0.0\build\portable-net+sl+win+wpa+wp\Fody.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Fody.2.0.0\build\portable-net+sl+win+wpa+wp\Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Fody.2.0.0\build\portable-net+sl+win+wpa+wp\Fody.targets'))" />
    <Error Condition="!Exists('..\packages\Costura.Fody.1.6.1\build\dotnet\Costura.Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Costura.Fody.1.6.1\build\dotnet\Costura.Fody.targets'))" />
  </Target>
  <Import Project="..\packages\Costura.Fody.1.6.1\build\dotnet\Costura.Fody.targets" Condition="Exists('..\packages\Costura.Fody.1.6.1\build\dotnet\Costura.Fody.targets')" />
</Project>